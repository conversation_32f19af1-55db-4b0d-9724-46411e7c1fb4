package config

import (
	"os"
	"strconv"
)

// Config chứa tất cả cấu hình của ứng dụng
type Config struct {
	Service    ServiceConfig    `json:"service"`
	Oracle     OracleConfig     `json:"oracle"`
	Ka<PERSON>ka      KafkaConfig      `json:"kafka"`
	Opensearch OpensearchConfig `json:"opensearch"`
}

// ServiceConfig cấu hình cho service
type ServiceConfig struct {
	Name string `json:"name" env:"SERVICE_NAME"`
	Port int    `json:"port" env:"SERVICE_PORT"`
}

// OracleConfig cấu hình cho Oracle database
type OracleConfig struct {
	Username         string `json:"username" env:"MSP_USERNAME"`
	Password         string `json:"password" env:"MSP_PASSWORD"`
	Host             string `json:"host" env:"MSP_HOST"`
	Port             int    `json:"port" env:"MSP_PORT"`
	SID              string `json:"sid" env:"MSP_SID"`
	Timeout          string `json:"timeout" env:"MSP_TIMEOUT"`
	Charset          string `json:"charset" env:"MSP_CHARSET"`
	MaxOpenConns     int    `json:"max_open_conns" env:"MSP_MAX_OPEN_CONNS"`
	MaxIdleConns     int    `json:"max_idle_conns" env:"MSP_MAX_IDLE_CONNS"`
	ConnsMaxIdleTime int    `json:"conns_max_idle_time" env:"MSP_CONNS_MAX_IDLE_TIME"`
	ConnsMaxLifeTime int    `json:"conns_max_life_time" env:"MSP_CONNS_MAX_LIFE_TIME"`
}

// KafkaConfig cấu hình cho Kafka
type KafkaConfig struct {
	Broker             string `json:"broker" env:"KAFKA_BROKER"`
	Topic              string `json:"topic" env:"KAFKA_TOPIC"`
	GroupID            string `json:"group_id" env:"KAFKA_GROUP_ID"`
	ConsumerCount      int    `json:"consumer_count" env:"KAFKA_CONSUMER_COUNT"`
	ConsumerTimeout    int    `json:"consumer_timeout" env:"KAFKA_CONSUMER_TIMEOUT"`
	ConsumerMaxRetries int    `json:"consumer_max_retries" env:"KAFKA_CONSUMER_MAX_RETRIES"`
}

// OpensearchConfig cấu hình cho Opensearch
type OpensearchConfig struct {
	URL         string `json:"url" env:"OPENSEARCH_URL"`
	Username    string `json:"username" env:"OPENSEARCH_USERNAME"`
	Password    string `json:"password" env:"OPENSEARCH_PASSWORD"`
	Index       string `json:"index" env:"OPENSEARCH_INDEX"`
	SearchIndex string `json:"search_index" env:"OPENSEARCH_SEARCH_INDEX"`
}

// LoadConfig đọc cấu hình từ biến môi trường
func LoadConfig() (*Config, error) {
	config := &Config{}

	// Service config
	config.Service.Name = getEnvString("SERVICE_NAME", "ma-opensearch-service")
	config.Service.Port = getEnvInt("SERVICE_PORT", 8080)

	// Oracle config MSP
	config.Oracle.Username = getEnvString("MSP_USERNAME", "onefraud")
	config.Oracle.Password = getEnvString("MSP_PASSWORD", "onefraud")
	config.Oracle.Host = getEnvString("MSP_HOST", "db3.onepay.vn")
	config.Oracle.Port = getEnvInt("MSP_PORT", 1521)
	config.Oracle.SID = getEnvString("MSP_SID", "ORCL")
	config.Oracle.Timeout = getEnvString("MSP_TIMEOUT", "300")
	config.Oracle.Charset = getEnvString("MSP_CHARSET", "UTF8")
	config.Oracle.MaxOpenConns = getEnvInt("MSP_MAX_OPEN_CONNS", 10)
	config.Oracle.MaxIdleConns = getEnvInt("MSP_MAX_IDLE_CONNS", 10)
	config.Oracle.ConnsMaxIdleTime = getEnvInt("MSP_CONNS_MAX_IDLE_TIME", 300)
	config.Oracle.ConnsMaxLifeTime = getEnvInt("MSP_CONNS_MAX_LIFE_TIME", 600)

	// Kafka config
	config.Kafka.Broker = getEnvString("KAFKA_BROKER", "localhost:9092")
	config.Kafka.Topic = getEnvString("KAFKA_TOPIC", "onefraud")
	config.Kafka.GroupID = getEnvString("KAFKA_GROUP_ID", "onefraud")
	config.Kafka.ConsumerCount = getEnvInt("KAFKA_CONSUMER_COUNT", 1)
	config.Kafka.ConsumerTimeout = getEnvInt("KAFKA_CONSUMER_TIMEOUT", 10000)
	config.Kafka.ConsumerMaxRetries = getEnvInt("KAFKA_CONSUMER_MAX_RETRIES", 3)

	// Opensearch config
	config.Opensearch.URL = getEnvString("OPENSEARCH_URL", "http://localhost:9200")
	config.Opensearch.Username = getEnvString("OPENSEARCH_USERNAME", "admin")
	config.Opensearch.Password = getEnvString("OPENSEARCH_PASSWORD", "admin")
	config.Opensearch.Index = getEnvString("OPENSEARCH_INDEX", "onefraud")
	config.Opensearch.SearchIndex = getEnvString("OPENSEARCH_SEARCH_INDEX", "onefraud_search")

	return config, nil
}

// getEnvString lấy giá trị string từ biến môi trường với giá trị mặc định
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt lấy giá trị int từ biến môi trường với giá trị mặc định
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
