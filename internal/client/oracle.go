package client

import (
	"database/sql"
	"fmt"
	"regexp"
	"time"

	"git.onepay.vn/onepay/ma-opensearch-service/config"
	go_ora "github.com/sijms/go-ora/v2"
)

func NewOracleClient(cfg *config.OracleConfig) (db *sql.DB, err error) {
	urlOptions := map[string]string{
		"SID":     cfg.SID,
		"TIMEOUT": cfg.Timeout,
		"CHARSET": cfg.Charset,
	}

	connStr := go_ora.BuildUrl(cfg.Host, cfg.Port, "", cfg.Username, cfg.Password, urlOptions)
	db, err = sql.Open("oracle", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open connection: %v", err)
	}

	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	db.SetConnMaxIdleTime(time.Duration(cfg.ConnsMaxIdleTime) * time.Second)
	db.SetConnMaxLifetime(time.Duration(cfg.ConnsMaxLifeTime) * time.Second)
	err = db.Ping()
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	fmt.Println("Connect Oracle database successfully:", db.Stats(), ",Connection Info:", maskPassword(connStr))
	return db, nil
}

func maskPassword(connectionString string) string {
	// Define the pattern to match the username and password part in the connection string
	passwordPattern := `(//[^:]+:)([^@]+)`

	// Compile the regular expression
	re := regexp.MustCompile(passwordPattern)

	// Replace the password with a masked version
	maskedConnectionString := re.ReplaceAllString(connectionString, `$1********`)

	return maskedConnectionString
}
